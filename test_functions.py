#!/usr/bin/env python3
"""
测试脚本 - 验证功能3和功能4的简化版本
"""

def test_movement_sequence_parsing():
    """测试移动序列解析功能"""
    print("=== 测试移动序列解析 ===")
    
    test_cases = [
        "W3",           # 单个命令
        "W3 A2 S1 D4",  # 复杂序列
        "w5 a3",        # 小写测试
        "W0",           # 无效次数
        "X3",           # 无效方向
        "W",            # 缺少次数
        "W3A2",         # 没有空格分隔
        "",             # 空输入
    ]
    
    KEYMAP = {"W": "51", "A": "29", "S": "47", "D": "32", "J": "38"}
    
    for test_input in test_cases:
        print(f"\n测试输入: '{test_input}'")
        
        if not test_input:
            print("❌ 输入为空")
            continue
            
        sequence_input = test_input.upper()
        commands = sequence_input.split()
        movement_plan = []
        
        for cmd in commands:
            if len(cmd) < 2:
                print(f"❌ 无效命令格式: {cmd}")
                continue
            
            direction = cmd[0]
            try:
                count = int(cmd[1:])
            except ValueError:
                print(f"❌ 无效数字: {cmd[1:]}")
                continue
            
            if direction not in KEYMAP or direction == 'J':
                print(f"❌ 无效方向: {direction}")
                continue
            
            if count <= 0:
                print(f"❌ 次数必须大于0: {count}")
                continue
            
            movement_plan.append({
                'direction': direction,
                'count': count,
                'keycode': KEYMAP[direction]
            })
        
        if movement_plan:
            plan_str = " → ".join([f"{p['direction']}{p['count']}" for p in movement_plan])
            print(f"✓ 解析成功: {plan_str}")
        else:
            print("❌ 没有有效的移动命令")

def test_coordinate_parsing():
    """测试坐标解析功能"""
    print("\n=== 测试坐标解析 ===")
    
    test_cases = [
        "540 1200",     # 正常坐标
        "100 200",      # 小坐标
        "0 0",          # 零坐标
        "-100 200",     # 负坐标
        "100",          # 缺少y坐标
        "100 200 300",  # 多余坐标
        "abc 200",      # 非数字
        "100 def",      # 非数字
        "",             # 空输入
        "   ",          # 空格
    ]
    
    for coords_input in test_cases:
        print(f"\n测试输入: '{coords_input}'")
        
        if not coords_input.strip():
            print("❌ 输入为空")
            continue
        
        try:
            coords = coords_input.split()
            if len(coords) != 2:
                print("❌ 格式错误，需要两个数字")
                continue
                
            x, y = int(coords[0]), int(coords[1])
            if x < 0 or y < 0:
                print("❌ 坐标不能为负数")
                continue
            
            print(f"✓ 解析成功: ({x}, {y})")
            
        except ValueError:
            print("❌ 包含非数字字符")
        except IndexError:
            print("❌ 坐标格式错误")

if __name__ == "__main__":
    print("功能3和功能4的解析逻辑测试")
    test_movement_sequence_parsing()
    test_coordinate_parsing()
    print("\n测试完成！")
