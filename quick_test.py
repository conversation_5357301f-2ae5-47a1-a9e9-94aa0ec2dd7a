#!/usr/bin/env python3
"""
快速测试脚本 - 用于验证不同的按键方法
"""
import subprocess
import time

def execute_adb_command(command):
    """执行ADB命令并返回结果"""
    print(f"执行: {command}")
    try:
        result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ 成功")
            return True
        else:
            print(f"✗ 失败: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ 错误: {e}")
        return False

def test_keyevent_methods():
    """测试不同的keyevent方法"""
    keycode = "51"  # W键
    
    print("=== 测试不同的按键方法 ===")
    print("请观察游戏角色是否移动\n")
    
    # 方法1: 普通keyevent
    print("1. 测试普通keyevent...")
    input("按回车开始测试普通keyevent: ")
    execute_adb_command(f"adb shell input keyevent {keycode}")
    response = input("角色是否移动了？(y/n): ").strip().lower()
    print(f"普通keyevent: {'有效' if response == 'y' else '无效'}\n")
    
    # 方法2: longpress keyevent
    print("2. 测试longpress keyevent...")
    input("按回车开始测试longpress keyevent: ")
    execute_adb_command(f"adb shell input keyevent --longpress {keycode}")
    response = input("角色是否移动了？(y/n): ").strip().lower()
    print(f"longpress keyevent: {'有效' if response == 'y' else '无效'}\n")
    
    # 方法3: 多次快速keyevent
    print("3. 测试多次快速keyevent...")
    input("按回车开始测试多次快速keyevent (5次): ")
    for i in range(5):
        execute_adb_command(f"adb shell input keyevent {keycode}")
        time.sleep(0.05)  # 50ms间隔
    response = input("角色是否移动了？(y/n): ").strip().lower()
    print(f"多次快速keyevent: {'有效' if response == 'y' else '无效'}\n")
    
    # 方法4: 持续keyevent
    print("4. 测试持续keyevent...")
    input("按回车开始测试持续keyevent (持续1秒): ")
    start_time = time.time()
    while time.time() - start_time < 1.0:
        execute_adb_command(f"adb shell input keyevent {keycode}")
        time.sleep(0.1)
    response = input("角色是否移动了？(y/n): ").strip().lower()
    print(f"持续keyevent: {'有效' if response == 'y' else '无效'}\n")

if __name__ == "__main__":
    # 检查ADB连接
    print("检查ADB连接...")
    if not execute_adb_command("adb devices"):
        print("ADB连接失败，请检查设备连接")
        exit(1)
    
    test_keyevent_methods()
    print("测试完成！")
