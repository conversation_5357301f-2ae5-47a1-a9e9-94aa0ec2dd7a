#!/usr/bin/env python3
"""
简化功能测试脚本 - 专门测试移动序列和屏幕点击
"""
import time
import subprocess
import logging

# 设置简单日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# 键位映射
KEYMAP = {
    "W": "51",  # 向上
    "A": "29",  # 向左  
    "S": "47",  # 向下
    "D": "32"   # 向右
}

def execute_adb_command(command):
    """执行ADB命令"""
    try:
        result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except Exception:
        return False

def press_key_longpress(keycode, times, delay=0.3):
    """执行longpress按键"""
    for i in range(times):
        command = f"adb shell input keyevent --longpress {keycode}"
        success = execute_adb_command(command)
        if not success:
            return False
        if i < times - 1:
            time.sleep(delay)
    return True

def tap_screen(x, y):
    """屏幕点击"""
    command = f"adb shell input tap {x} {y}"
    return execute_adb_command(command)

def test_movement_sequence():
    """测试移动序列功能"""
    print("\n=== 移动序列测试 ===")
    
    test_sequences = [
        "W3",           # 向上3次
        "A2 D2",        # 向左2次，向右2次
        "W1 A1 S1 D1",  # 四个方向各1次
        "W5 A3 S5 D3"   # 复杂序列
    ]
    
    for seq in test_sequences:
        print(f"\n测试序列: {seq}")
        input("按回车执行...")
        
        # 解析序列
        commands = seq.split()
        for cmd in commands:
            direction = cmd[0]
            count = int(cmd[1:])
            keycode = KEYMAP[direction]
            
            print(f"执行: {direction}{count}", end=" ")
            success = press_key_longpress(keycode, count)
            print("✓" if success else "❌")
            
            time.sleep(0.5)  # 步骤间等待
        
        print("序列完成！")

def test_screen_tap():
    """测试屏幕点击功能"""
    print("\n=== 屏幕点击测试 ===")
    
    test_coords = [
        (540, 960),   # 屏幕中心
        (100, 100),   # 左上角
        (980, 1800),  # 右下角
        (540, 200)    # 顶部中心
    ]
    
    for x, y in test_coords:
        print(f"\n测试点击: ({x}, {y})")
        input("按回车执行...")
        
        success = tap_screen(x, y)
        print(f"点击结果: {'✓' if success else '❌'}")

if __name__ == "__main__":
    print("=== 简化功能测试 ===")
    print("1. 移动序列测试")
    print("2. 屏幕点击测试")
    
    choice = input("选择测试 (1/2): ").strip()
    
    if choice == "1":
        test_movement_sequence()
    elif choice == "2":
        test_screen_tap()
    else:
        print("无效选择")
