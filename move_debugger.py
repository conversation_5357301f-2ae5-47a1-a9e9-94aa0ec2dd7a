import time
import subprocess
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('move_debugger.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ADB Keycode Mappings for WASD and a common "action" key (e.g., J for Enter/OK)
# 这些是标准的Android keycode值
KEYCODE_W = "51"  # W键 - 向上移动
KEYCODE_A = "29"  # A键 - 向左移动
KEYCODE_S = "47"  # S键 - 向下移动
KEYCODE_D = "32"  # D键 - 向右移动
KEYCODE_ACTION = "38"  # J键 - 通常用于确认/动作

# 键位映射字典，方便查找
KEYMAP = {
    "W": KEYCODE_W,
    "A": KEYCODE_A,
    "S": KEYCODE_S,
    "D": KEYCODE_D,
    "J": KEYCODE_ACTION
}

def check_adb_connection():
    """检查ADB连接状态"""
    logger.info("检查ADB连接状态...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        output = result.stdout.strip()
        logger.info(f"ADB devices输出: {output}")

        lines = output.split('\n')
        if len(lines) < 2:
            logger.error("没有检测到连接的设备")
            return False

        devices = [line for line in lines[1:] if line.strip() and 'device' in line]
        if not devices:
            logger.error("没有检测到在线的设备")
            return False

        logger.info(f"检测到 {len(devices)} 个设备: {devices}")
        return True

    except subprocess.TimeoutExpired:
        logger.error("ADB命令超时")
        return False
    except FileNotFoundError:
        logger.error("找不到ADB命令，请确保ADB已安装并添加到PATH")
        return False
    except Exception as e:
        logger.error(f"检查ADB连接时出错: {e}")
        return False

def execute_adb_command(command):
    """执行ADB命令并返回结果"""
    logger.info(f"执行ADB命令: {command}")
    try:
        result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info(f"命令执行成功: {result.stdout.strip()}")
            return True, result.stdout.strip()
        else:
            logger.error(f"命令执行失败 (返回码: {result.returncode}): {result.stderr.strip()}")
            return False, result.stderr.strip()
    except subprocess.TimeoutExpired:
        logger.error(f"命令执行超时: {command}")
        return False, "命令超时"
    except Exception as e:
        logger.error(f"执行命令时出错: {e}")
        return False, str(e)

def press_key_longpress(keycode, duration=0.5):
    """模拟长按键，使用longpress参数"""
    key_name = next((k for k, v in KEYMAP.items() if v == keycode), f"keycode_{keycode}")
    logger.info(f"执行长按操作: {key_name} (keycode: {keycode}), 持续时间: {duration}秒")

    # 检查ADB连接
    if not check_adb_connection():
        logger.error("ADB连接检查失败，无法执行按键操作")
        return False

    # 使用longpress参数
    command = f"adb shell input keyevent --longpress {keycode}"
    success, output = execute_adb_command(command)

    if success:
        logger.info(f"长按 {key_name} 键成功")
        # 额外等待指定的持续时间
        if duration > 0:
            time.sleep(duration)
    else:
        logger.error(f"长按 {key_name} 键失败: {output}")

    return success

def press_key_with_duration(keycode, duration=0.5):
    """使用按下和松开事件模拟持续按压"""
    key_name = next((k for k, v in KEYMAP.items() if v == keycode), f"keycode_{keycode}")
    logger.info(f"执行持续按压: {key_name} (keycode: {keycode}), 持续时间: {duration}秒")

    # 检查ADB连接
    if not check_adb_connection():
        logger.error("ADB连接检查失败，无法执行按键操作")
        return False

    # 发送按下事件
    down_command = f"adb shell sendevent /dev/input/event0 1 {keycode} 1"
    logger.info(f"发送按下事件: {down_command}")
    success_down, output_down = execute_adb_command(down_command)

    if not success_down:
        logger.error(f"按下事件失败: {output_down}")
        return False

    # 发送同步事件
    sync_command = "adb shell sendevent /dev/input/event0 0 0 0"
    execute_adb_command(sync_command)

    # 持续按压指定时间
    logger.info(f"持续按压 {duration} 秒...")
    time.sleep(duration)

    # 发送松开事件
    up_command = f"adb shell sendevent /dev/input/event0 1 {keycode} 0"
    logger.info(f"发送松开事件: {up_command}")
    success_up, output_up = execute_adb_command(up_command)

    # 发送同步事件
    execute_adb_command(sync_command)

    if success_up:
        logger.info(f"持续按压 {key_name} 键完成")
    else:
        logger.error(f"松开事件失败: {output_up}")

    return success_down and success_up

def press_key_optimized(keycode, times, delay=0.5):
    """优化的按键函数，专门使用longpress方法"""
    key_name = next((k for k, v in KEYMAP.items() if v == keycode), f"keycode_{keycode}")
    logger.info(f"开始执行按键操作: {key_name} (keycode: {keycode})")
    logger.info(f"参数: 次数={times}, 间隔={delay}秒 (使用longpress方法)")

    # 检查ADB连接
    if not check_adb_connection():
        logger.error("ADB连接检查失败，无法执行按键操作")
        return False

    success_count = 0
    for i in range(times):
        logger.info(f"第 {i+1}/{times} 次长按...")

        # 使用longpress方法
        command = f"adb shell input keyevent --longpress {keycode}"
        success, output = execute_adb_command(command)

        if success:
            success_count += 1
            logger.info(f"第 {i+1} 次长按成功")
        else:
            logger.error(f"第 {i+1} 次长按失败: {output}")

        # 按键间延迟
        if i < times - 1:  # 最后一次不需要延迟
            logger.info(f"等待 {delay} 秒...")
            time.sleep(delay)

    logger.info(f"按键操作完成: 成功 {success_count}/{times} 次")
    return success_count == times

def press_key(keycode, times, delay=0.5, press_duration=None, method=None):
    """兼容性函数，统一调用优化的longpress方法"""
    # 忽略不需要的参数，只使用longpress方法
    return press_key_optimized(keycode, times, delay)

def calibrate_movement():
    """移动距离校准功能"""
    logger.info("开始移动距离校准...")
    print("\n=== 移动距离校准 ===")
    print("这个功能帮助您确定按键次数与移动距离的关系")

    # 选择校准方向
    direction = input("选择校准方向 (W/A/S/D，默认W): ").strip().upper() or "W"
    if direction not in KEYMAP or direction == 'J':
        print("使用默认方向 W (向上)")
        direction = "W"

    keycode = KEYMAP[direction]
    direction_name = {"W": "向上", "A": "向左", "S": "向下", "D": "向右"}[direction]

    print(f"\n开始校准 {direction} 键 ({direction_name}) 的移动距离")
    print("请按照提示操作，记录角色的移动情况")

    # 校准数据存储
    calibration_data = []

    for test_count in [1, 2, 3, 5, 10]:
        print(f"\n--- 测试按 {test_count} 次 {direction} 键 ---")
        input(f"请记住角色当前位置，然后按回车开始测试 {test_count} 次按键: ")

        success = press_key_optimized(keycode, test_count, 0.3)

        if success:
            distance = input(f"按键完成！角色{direction_name}移动了多少距离？(输入数字或描述): ").strip()
            calibration_data.append({
                'count': test_count,
                'distance': distance,
                'direction': direction_name
            })
            logger.info(f"校准数据: {test_count}次按键 = {distance} 距离")
            print(f"✓ 记录: {test_count}次{direction}键 = {distance}")
        else:
            print("❌ 按键发送失败")

    # 显示校准结果
    print(f"\n=== {direction}键 ({direction_name}) 校准结果 ===")
    for data in calibration_data:
        print(f"{data['count']}次按键 → {data['distance']}")

    # 保存校准数据到文件
    try:
        with open('movement_calibration.txt', 'a', encoding='utf-8') as f:
            f.write(f"\n=== {direction}键校准 - {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            for data in calibration_data:
                f.write(f"{data['count']}次按键 → {data['distance']}\n")
        print(f"\n✓ 校准数据已保存到 movement_calibration.txt")
        logger.info("校准数据已保存到文件")
    except Exception as e:
        print(f"❌ 保存校准数据失败: {e}")
        logger.error(f"保存校准数据失败: {e}")

def execute_movement_sequence():
    """执行移动序列"""
    logger.info("开始执行移动序列...")
    print("\n=== 移动序列执行 ===")
    print("输入移动序列，格式: W3 A2 S1 D4 (表示向上3次，向左2次，向下1次，向右4次)")
    print("或者输入单个命令，如: W5 (向上5次)")

    sequence_input = input("请输入移动序列: ").strip().upper()
    if not sequence_input:
        print("❌ 输入为空")
        return

    # 解析移动序列
    commands = sequence_input.split()
    movement_plan = []

    for cmd in commands:
        if len(cmd) < 2:
            print(f"❌ 无效命令格式: {cmd}")
            continue

        direction = cmd[0]
        try:
            count = int(cmd[1:])
        except ValueError:
            print(f"❌ 无效数字: {cmd[1:]}")
            continue

        if direction not in KEYMAP or direction == 'J':
            print(f"❌ 无效方向: {direction}")
            continue

        if count <= 0:
            print(f"❌ 次数必须大于0: {count}")
            continue

        movement_plan.append({
            'direction': direction,
            'count': count,
            'keycode': KEYMAP[direction]
        })

    if not movement_plan:
        print("❌ 没有有效的移动命令")
        return

    # 显示执行计划
    print(f"\n=== 执行计划 ===")
    direction_names = {"W": "向上", "A": "向左", "S": "向下", "D": "向右"}
    for i, plan in enumerate(movement_plan, 1):
        print(f"{i}. {plan['direction']}键 ({direction_names[plan['direction']]}) × {plan['count']}次")

    delay = float(input("\n按键间隔时间(秒，默认0.3): ") or "0.3")
    step_delay = float(input("步骤间等待时间(秒，默认1.0): ") or "1.0")

    input("\n按回车开始执行移动序列...")

    # 执行移动序列
    for i, plan in enumerate(movement_plan, 1):
        print(f"\n执行步骤 {i}/{len(movement_plan)}: {plan['direction']}键 × {plan['count']}次")
        success = press_key_optimized(plan['keycode'], plan['count'], delay)

        if success:
            print(f"✓ 步骤 {i} 完成")
        else:
            print(f"❌ 步骤 {i} 失败")
            break

        # 步骤间等待
        if i < len(movement_plan):
            print(f"等待 {step_delay} 秒...")
            time.sleep(step_delay)

    print("\n✓ 移动序列执行完成！")

def tap_screen(x, y):
    """模拟屏幕点击，带详细日志"""
    logger.info(f"开始执行屏幕点击: 坐标 ({x}, {y})")

    # 检查ADB连接
    if not check_adb_connection():
        logger.error("ADB连接检查失败，无法执行点击操作")
        return False

    command = f"adb shell input tap {x} {y}"
    success, output = execute_adb_command(command)

    if success:
        logger.info(f"屏幕点击成功: ({x}, {y})")
    else:
        logger.error(f"屏幕点击失败: {output}")

    return success

def get_screen_info():
    """获取屏幕信息，用于调试"""
    logger.info("获取屏幕信息...")
    command = "adb shell wm size"
    success, output = execute_adb_command(command)
    if success:
        logger.info(f"屏幕尺寸: {output}")
    else:
        logger.error("无法获取屏幕尺寸")

    command = "adb shell wm density"
    success, output = execute_adb_command(command)
    if success:
        logger.info(f"屏幕密度: {output}")
    else:
        logger.error("无法获取屏幕密度")

def test_different_press_methods():
    """测试不同的按键方法"""
    logger.info("开始按键方法测试...")
    print("\n=== 按键方法测试 ===")
    print("这将测试不同的按键方法，找出游戏能识别的方式")

    methods = [
        ("traditional", "传统keyevent方法"),
        ("longpress", "长按keyevent方法"),
        ("duration", "sendevent持续按压方法")
    ]

    test_key = input("选择测试键位 (W/A/S/D，默认W): ").strip().upper() or "W"
    if test_key not in KEYMAP or test_key == 'J':
        print("使用默认键位 W")
        test_key = "W"

    keycode = KEYMAP[test_key]

    for method_code, method_name in methods:
        print(f"\n--- 测试 {method_name} ---")
        input(f"请观察屏幕，按回车测试 {test_key} 键 ({method_name}): ")

        if method_code == "traditional":
            # 传统方法
            command = f"adb shell input keyevent {keycode}"
            success, _ = execute_adb_command(command)
        elif method_code == "longpress":
            # 长按方法
            success = press_key_longpress(keycode, 0.5)
        else:  # duration
            # 持续按压方法
            success = press_key_with_duration(keycode, 0.5)

        if success:
            response = input(f"{method_name} 测试完成，角色是否移动了？(y/n): ").strip().lower()
            if response == 'y':
                logger.info(f"{method_name} 工作正常")
                print(f"✓ {method_name} 工作正常")
            else:
                logger.warning(f"{method_name} 可能无效")
                print(f"✗ {method_name} 可能无效")
        else:
            logger.error(f"{method_name} 发送失败")
            print(f"✗ {method_name} 发送失败")

def test_single_keypress():
    """测试单次按键是否有效（使用最佳方法）"""
    logger.info("开始单次按键测试...")
    print("\n=== 单次按键测试 ===")
    print("这将使用长按方法测试每个方向键")

    for key_name, keycode in KEYMAP.items():
        if key_name == 'J':  # 跳过动作键
            continue
        print(f"\n测试 {key_name} 键...")
        input(f"请观察屏幕，然后按回车键测试 {key_name} 键: ")
        success = press_key_longpress(keycode, 0.5)
        if success:
            response = input(f"{key_name} 键测试完成，角色是否移动了？(y/n): ").strip().lower()
            if response == 'y':
                logger.info(f"{key_name} 键工作正常")
                print(f"✓ {key_name} 键工作正常")
            else:
                logger.warning(f"{key_name} 键可能无效")
                print(f"✗ {key_name} 键可能无效")
        else:
            logger.error(f"{key_name} 键发送失败")
            print(f"✗ {key_name} 键发送失败")

if __name__ == "__main__":
    logger.info("=== ADB游戏自动化调试器启动 ===")

    # 启动时检查ADB连接
    print("正在检查ADB连接...")
    if not check_adb_connection():
        print("❌ ADB连接失败！请检查：")
        print("1. 手机是否已连接并开启USB调试")
        print("2. ADB是否已安装并添加到PATH")
        print("3. 是否已授权此电脑进行USB调试")
        input("按回车键退出...")
        exit(1)

    print("✓ ADB连接正常")
    get_screen_info()

    while True:
        print("\n" + "="*50)
        print("        ADB游戏自动化调试器 (优化版)")
        print("="*50)
        print("1. 移动测试 (W/A/S/D) - 使用longpress方法")
        print("2. 移动距离校准 - 确定按键次数与移动距离关系")
        print("3. 移动序列执行 - 执行复杂的移动组合")
        print("4. 屏幕点击测试")
        print("5. 单次按键测试")
        print("6. 查看ADB连接状态")
        print("7. 查看屏幕信息")
        print("Q. 退出")

        choice = input("\n请选择操作: ").strip().upper()

        if choice == 'Q':
            logger.info("用户选择退出")
            break

        elif choice == '1':
            print("\n=== 移动测试 (Longpress方法) ===")
            key_choice = input("请输入要按的键 (W/A/S/D): ").strip().upper()

            if key_choice not in KEYMAP or key_choice == 'J':
                print("❌ 无效的键位，请输入 W、A、S 或 D")
                continue

            try:
                press_count = int(input(f"按 '{key_choice}' 键多少次? "))
                if press_count <= 0:
                    print("❌ 次数必须大于0")
                    continue

                delay = float(input("按键间隔时间(秒，默认0.3): ") or "0.3")
                if delay < 0:
                    print("❌ 延迟时间不能为负数")
                    continue

                direction_names = {"W": "向上", "A": "向左", "S": "向下", "D": "向右"}
                print(f"\n即将执行: {key_choice}键 ({direction_names[key_choice]}) × {press_count}次，间隔{delay}秒")
                input("请观察屏幕，然后按回车开始...")

                success = press_key_optimized(KEYMAP[key_choice], press_count, delay)
                if success:
                    print("✓ 按键命令发送完成")
                else:
                    print("❌ 按键命令发送失败")

            except ValueError:
                print("❌ 请输入有效的数字")

        elif choice == '2':
            calibrate_movement()

        elif choice == '3':
            execute_movement_sequence()

        elif choice == '4':
            print("\n=== 屏幕点击测试 ===")
            try:
                coords_input = input("请输入点击坐标 (格式: x y，例如: 540 1200): ").strip()
                coords = coords_input.split()
                if len(coords) != 2:
                    print("❌ 格式错误，请输入两个数字，用空格分隔")
                    continue

                x, y = int(coords[0]), int(coords[1])
                if x < 0 or y < 0:
                    print("❌ 坐标不能为负数")
                    continue

                print(f"\n即将点击坐标: ({x}, {y})")
                input("请观察屏幕，然后按回车开始...")

                success = tap_screen(x, y)
                if success:
                    print("✓ 点击命令发送完成")
                else:
                    print("❌ 点击命令发送失败")

            except ValueError:
                print("❌ 请输入有效的坐标数字")
            except IndexError:
                print("❌ 坐标格式错误")

        elif choice == '5':
            test_single_keypress()

        elif choice == '6':
            print("\n=== ADB连接状态 ===")
            if check_adb_connection():
                print("✓ ADB连接正常")
            else:
                print("❌ ADB连接异常")

        elif choice == '7':
            print("\n=== 屏幕信息 ===")
            get_screen_info()

        else:
            print("❌ 无效选择，请重新输入")
