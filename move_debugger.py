import os
import time

# ADB Keycode Mappings for WASD and a common "action" key (e.g., J for Enter/OK)
KEYCODE_W = "51"
KEYCODE_A = "29"
KEYCODE_S = "47"
KEYCODE_D = "32"
KEYCODE_ACTION = "38" # 'J' key, often used for confirmation

def press_key(keycode, times, delay=0.1):
    """Simulates pressing a key multiple times."""
    print(f"Executing {times} press(es) for keycode {keycode}...")
    for i in range(times):
        os.system(f"adb shell input keyevent {keycode}")
        # A small delay between presses is crucial for the game to register each event separately
        time.sleep(delay)
    print("Done.")

def tap_screen(x, y):
    """Simulates tapping a specific coordinate."""
    print(f"Tapping at ({x}, {y})...")
    os.system(f"adb shell input tap {x} {y}")
    print("Done.")

if __name__ == "__main__":
    while True:
        print("\n" + "="*30)
        print("  ADB Game Automation Debugger")
        print("="*30)
        print("1. Move (W/A/S/D)")
        print("2. Tap Screen")
        print("Q. Quit")
        
        choice = input("Enter your choice: ").strip().upper()

        if choice == 'Q':
            break
        
        if choice == '1':
            key_choice = input("Enter key to press (W, A, S, D): ").strip().upper()
            keymap = {"W": KEYCODE_W, "A": KEYCODE_A, "S": KEYCODE_S, "D": KEYCODE_D}
            
            if key_choice not in keymap:
                print("Invalid key. Please enter W, A, S, or D.")
                continue
            
            try:
                press_count = int(input(f"How many times to press '{key_choice}'? "))
                press_key(keymap[key_choice], press_count)
            except ValueError:
                print("Invalid number. Please enter an integer.")
        
        elif choice == '2':
            try:
                coords = input("Enter coordinates to tap (e.g., 540 1200): ").split()
                x, y = int(coords[0]), int(coords[1])
                tap_screen(x, y)
            except (ValueError, IndexError):
                print("Invalid format. Please enter coordinates as 'x y'.")

        else:
            print("Invalid choice, please try again.")
