# 使用示例 - 功能3和功能4

## 功能3：移动序列执行（简化版）

### 特点
- ✅ 无需设置复杂参数，使用默认值
- ✅ 支持连续测试，不会退出到主菜单
- ✅ 简洁的输入格式和即时反馈

### 使用方法

1. 运行脚本：`python move_debugger.py`
2. 选择菜单：`3. 移动序列执行`
3. 输入移动序列，例如：

```
请输入移动序列 (或 q 退出): W3 A2 S1 D4
执行计划: W3 → A2 → S1 → D4
执行: W键 × 3次 ✓
执行: A键 × 2次 ✓
执行: S键 × 1次 ✓
执行: D键 × 4次 ✓
✓ 移动序列执行完成！(4/4)

请输入移动序列 (或 q 退出): W5
执行计划: W5
执行: W键 × 5次 ✓
✓ 移动序列执行完成！(1/1)

请输入移动序列 (或 q 退出): q
```

### 移动序列语法

| 格式 | 说明 | 示例 |
|------|------|------|
| `W3` | 向上移动3次 | `W3` |
| `A2 D1` | 向左2次，然后向右1次 | `A2 D1` |
| `W2 A1 S2 D1` | 复杂路径 | `W2 A1 S2 D1` |

### 默认参数
- **按键间隔**: 0.3秒
- **步骤间等待**: 0.8秒
- **按键方法**: longpress

### 常用移动模式

#### 正方形路径
```
W5 D5 S5 A5
```

#### 十字形路径
```
W3 S6 W3 A3 D6
```

#### 对角线移动
```
W2 D2 S2 A2
```

## 功能4：屏幕点击测试（简化版）

### 特点
- ✅ 直接输入坐标即可点击
- ✅ 支持连续测试多个坐标
- ✅ 即时反馈点击结果

### 使用方法

1. 运行脚本：`python move_debugger.py`
2. 选择菜单：`4. 屏幕点击测试`
3. 输入坐标，例如：

```
请输入点击坐标 (或 q 退出): 540 1200
点击坐标: (540, 1200) ✓

请输入点击坐标 (或 q 退出): 100 200
点击坐标: (100, 200) ✓

请输入点击坐标 (或 q 退出): 300 800
点击坐标: (300, 800) ✓

请输入点击坐标 (或 q 退出): q
```

### 坐标格式
- **正确格式**: `x y` (用空格分隔)
- **示例**: `540 1200`, `100 200`, `0 0`

### 常用坐标位置

#### 屏幕区域（以1080x1920为例）
```
左上角: 0 0
屏幕中心: 540 960
右下角: 1080 1920
```

#### 游戏界面常用位置
```
背包按钮: 50 100
设置按钮: 1030 100
确认按钮: 540 1200
取消按钮: 300 1200
```

## 连续测试工作流程

### 移动测试流程
1. 进入功能3
2. 测试简单移动：`W1`, `A1`, `S1`, `D1`
3. 测试复杂序列：`W3 A2 S3 D2`
4. 根据效果调整序列
5. 输入 `q` 退出

### 点击测试流程
1. 进入功能4
2. 测试屏幕四角：`0 0`, `1080 0`, `0 1920`, `1080 1920`
3. 测试游戏按钮位置
4. 验证点击响应
5. 输入 `q` 退出

## 错误处理

### 移动序列常见错误
- `❌ 无效命令格式: W` → 缺少次数，应该是 `W1`
- `❌ 无效方向: X` → 只支持 W/A/S/D
- `❌ 次数必须大于0: 0` → 次数必须是正整数

### 坐标点击常见错误
- `❌ 格式错误，需要两个数字` → 应该是 `x y` 格式
- `❌ 坐标不能为负数` → 坐标必须是非负整数
- `❌ 包含非数字字符` → 只能输入数字

## 实际应用场景

### 农场游戏自动化
```
# 收菜路径
W3 D2 S1 D2 S1 D2 S1 A6 W3

# 播种路径  
W2 D1 S1 D1 S1 D1 S1 A3 W2

# 返回起点
A3 W3
```

### 界面操作组合
```
# 移动到目标位置
W5 D3

# 点击交互按钮
540 1200

# 移动到下一个位置
S2 D2

# 再次点击
540 1200
```

这种简化的设计让您可以快速迭代测试，找到最佳的移动和点击参数！
